"""
FastAPI数据发送服务
基于现有BSA代码重构，实现异步任务管理和数据发送功能
"""

import asyncio
import random
import logging
import os
import json
from datetime import datetime
from typing import Dict, Any, Optional, Literal
import requests
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 确保log目录存在
os.makedirs("log", exist_ok=True)


# 请求模型
class DataImportRequest(BaseModel):
    state: Literal["start", "stop"] = Field(default="start", description="任务状态：start启动任务，stop停止任务")
    test_id: int = Field(gt=0, description="测试ID，必须为正整数")
    group_size: int = Field(gt=0, description="子组大小，必须为正整数")
    interval: int = Field(default=2, gt=0, description="间隔时间（秒），必须为正整数，默认2秒")
    duration: int = Field(default=60, gt=0, description="持续时间（秒），必须为正整数，默认60秒")


# 响应模型
class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


# 全局任务管理器
class TaskManager:
    def __init__(self):
        self.tasks: Dict[int, asyncio.Task] = {}  # test_id -> Task
        self.task_status: Dict[int, Dict[str, Any]] = {}  # test_id -> status info

    def is_running(self, test_id: int) -> bool:
        """检查任务是否正在运行"""
        task = self.tasks.get(test_id)
        return task is not None and not task.done()

    def get_status(self, test_id: int) -> Dict[str, Any]:
        """获取任务状态"""
        if test_id in self.task_status:
            status = self.task_status[test_id].copy()
            status["is_running"] = self.is_running(test_id)
            return status
        return {"is_running": False, "message": "任务不存在"}

    async def stop_task(self, test_id: int) -> bool:
        """停止并删除任务"""
        if test_id in self.tasks:
            task = self.tasks[test_id]
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            del self.tasks[test_id]

        if test_id in self.task_status:
            self.task_status[test_id]["is_running"] = False
            self.task_status[test_id]["message"] = "任务已停止"

        return True

    def start_task(self, test_id: int, task_coro, task_params: Dict[str, Any]):
        """启动新任务"""
        # 创建异步任务
        task = asyncio.create_task(task_coro)
        self.tasks[test_id] = task

        # 初始化任务状态
        self.task_status[test_id] = {
            "test_id": test_id,
            "start_time": datetime.now().isoformat(),
            "is_running": True,
            "message": "任务运行中",
            "params": task_params,
            "sent_count": 0,
        }

        return task


# 数据生成器
class DataGenerator:
    def __init__(self, test_id: int, group_size: int):
        self.test_id = test_id
        self.group_size = group_size
        # 默认数据范围和字段值
        self.min_value = -101
        self.max_value = 101
        self.default_fields = {
            "f1": "过程-01", "f2": "操作员-02", "f3": "产品型号-03",
            "f4": "产品-04", "f5": "设备-05", "f6": "供应商-06",
            "f7": "客户-07", "f8": "批次-08", "f9": "工单编号-09",
        }

    def generate_single_record(self) -> Dict[str, Any]:
        """生成单条数据记录"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 生成随机整数数据
        data_values = [
            random.randint(self.min_value, self.max_value)
            for _ in range(self.group_size)
        ]

        return {
            "inspection_time": current_time,
            "measurements": [{"id": self.test_id, "data": data_values}],
            "metadata": self.default_fields,
        }


# 统一API客户端
class ApiClient:
    # 默认配置
    DEFAULT_CONFIG = {
        "email": "<EMAIL>",
        "authorization": "Bearer 24|BBOpNsI7onQRu8PMCJmetL8Yq8Nt1S9QA9Erw9X8",
        "auto_retry_count": 0,
        "base_url": "http://101.37.161.59:8002",
        "token_url": "http://101.37.161.59:8001/api/v1/user/getToken",
    }

    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self._init_config()
        self._init_api_settings()

    def _init_config(self):
        """初始化配置"""
        # 设置默认值
        self.email = self.DEFAULT_CONFIG["email"]
        self.authorization = self.DEFAULT_CONFIG["authorization"]
        self.auto_retry_count = self.DEFAULT_CONFIG["auto_retry_count"]
        self.base_url = self.DEFAULT_CONFIG["base_url"]
        self.token_url = self.DEFAULT_CONFIG["token_url"]

        # 加载配置文件
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    self._apply_config(config)
            except Exception as e:
                logger.error(f"配置文件读取失败，使用默认配置: {e}")
        else:
            logger.info("配置文件不存在，使用默认配置")
            self._save_config()

    def _apply_config(self, config: dict):
        """应用配置文件设置"""
        for key, default_value in self.DEFAULT_CONFIG.items():
            value = config.get(key)
            if value is not None and isinstance(value, type(default_value)):
                setattr(self, key, value)
            elif key != "authorization":  # authorization为空是正常的
                logger.warning(f"配置项 {key} 无效，使用默认值: {default_value}")

    def _init_api_settings(self):
        """初始化API相关设置"""
        self.target_url = f"{self.base_url}/api/v1/dataApi/import"
        self.headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
            "Connection": "keep-alive",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/BSA-SPC%20v4.html",
        }

    def _save_config(self):
        """保存当前配置到文件"""
        try:
            config = {key: getattr(self, key) for key in self.DEFAULT_CONFIG.keys()}
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")

    async def get_valid_authorization(self) -> Dict[str, Any]:
        """获取有效的Authorization"""
        # 如果配置为0，只使用配置文件中的Authorization
        if self.auto_retry_count == 0:
            if self.authorization:
                logger.info("使用配置文件中的Authorization")
                return {"success": True, "authorization": self.authorization}
            else:
                return {
                    "success": False,
                    "error": "配置文件中无Authorization且禁用自动获取",
                }

        # 首先测试现有Authorization
        if self.authorization and self._test_authorization(self.authorization):
            logger.info("使用配置文件中的Authorization")
            return {"success": True, "authorization": self.authorization}

        # 自动获取新Token
        for attempt in range(self.auto_retry_count):
            logger.info(f"获取新Token，第 {attempt + 1}/{self.auto_retry_count} 次尝试")

            try:
                response = requests.get(
                    self.token_url,
                    params={"email": self.email},
                    headers={"Accept": "*/*", "Accept-Language": "*/*"},
                    timeout=10,
                )
                response.raise_for_status()

                result = response.json()
                if result.get("status") and result.get("data", {}).get("token"):
                    token = result["data"]["token"]
                    authorization = f"Bearer {token}"

                    # 测试新Token
                    if self._test_authorization(authorization):
                        self.authorization = authorization
                        self._save_config()
                        logger.info("新Token获取并验证成功")
                        return {"success": True, "authorization": authorization}
                    else:
                        logger.warning("新Token验证失败")
                else:
                    logger.warning(f"Token获取失败: {result.get('message', '未知错误')}")

            except Exception as e:
                logger.warning(f"Token获取异常: {str(e)}")

        return {
            "success": False,
            "error": f"获取有效Authorization失败，已重试 {self.auto_retry_count} 次",
        }

    def _test_authorization(self, authorization: str) -> bool:
        """测试Authorization是否有效"""
        try:
            headers = self.headers.copy()
            headers["Authorization"] = authorization
            response = requests.post(self.target_url, headers=headers, json=[], timeout=5)
            return response.status_code != 401
        except Exception:
            return False

    async def send_data(self, data: Dict[str, Any], authorization: str = "") -> Dict[str, Any]:
        """发送数据到目标API"""
        try:
            headers = self.headers.copy()
            if authorization:
                headers["Authorization"] = authorization
            response = requests.post(self.target_url, headers=headers, json=[data], timeout=10)
            response.raise_for_status()
            return {"success": True, "response": response.json()}
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP错误: {e.response.status_code} - {e.response.reason}"
            logger.error(f"数据发送失败: {error_msg}")
            return {"success": False, "error": error_msg}
        except Exception as e:
            error_msg = str(e)
            logger.error(f"数据发送异常: {error_msg}")
            return {"success": False, "error": error_msg}


# 全局实例
app = FastAPI(title="数据发送服务", version="1.0.0")
task_manager = TaskManager()
api_client = ApiClient()


# 数据发送任务
async def data_sending_task(test_id: int, group_size: int, interval: int, duration: int, authorization: str = ""):
    """异步数据发送任务"""
    generator = DataGenerator(test_id, group_size)
    total_iterations = duration // interval
    sent_count = 0
    current_task = asyncio.current_task()

    # 创建任务专用日志记录器
    task_log_file = f"log/{test_id}.log"
    task_logger = logging.getLogger(f"task_{test_id}")
    task_logger.setLevel(logging.INFO)

    # 如果该测试ID的日志处理器不存在，则创建
    if not task_logger.handlers:
        file_handler = logging.FileHandler(task_log_file, encoding="utf-8")
        file_handler.setFormatter(logging.Formatter("%(asctime)s - %(message)s"))
        task_logger.addHandler(file_handler)
        task_logger.propagate = False  # 防止重复输出到根日志

    logger.info(f"任务 {test_id} 开始执行，预计发送 {total_iterations} 条数据")
    task_logger.info(f"任务开始 - 预计发送 {total_iterations} 条数据，间隔 {interval} 秒")

    try:
        for i in range(total_iterations):
            # 检查任务是否被取消
            if current_task and current_task.cancelled():
                logger.info(f"任务 {test_id} 被取消")
                task_logger.info("任务被取消")
                break

            # 生成并发送数据
            record = generator.generate_single_record()
            send_time = datetime.now()
            result = await api_client.send_data(record, authorization)

            sent_count += 1

            # 记录到任务专用日志文件
            if result["success"]:
                task_logger.info(f"第 {sent_count} 条数据发送成功 - 时间: {send_time.strftime('%H:%M:%S')} - 数据: {record['measurements'][0]['data']}")
            else:
                task_logger.error(f"第 {sent_count} 条数据发送失败 - 时间: {send_time.strftime('%H:%M:%S')} - 错误: {result.get('error', '未知错误')}")

            # 更新任务状态
            if test_id in task_manager.task_status:
                task_manager.task_status[test_id]["sent_count"] = sent_count
                task_manager.task_status[test_id]["last_send_time"] = (datetime.now().isoformat())

                if result["success"]:
                    task_manager.task_status[test_id]["message"] = (f"已发送 {sent_count}/{total_iterations} 条数据")
                    logger.debug(f"任务 {test_id} 成功发送第 {sent_count} 条数据")
                else:
                    error_msg = f"发送失败: {result.get('error', '未知错误')}"
                    task_manager.task_status[test_id]["message"] = error_msg
                    logger.error(f"任务 {test_id} 第 {sent_count} 条数据发送失败: {result.get('error', '未知错误')}")

            # 等待下一次发送
            if i < total_iterations - 1:  # 最后一次不需要等待
                await asyncio.sleep(interval)

        # 任务完成
        if test_id in task_manager.task_status:
            task_manager.task_status[test_id]["is_running"] = False
            task_manager.task_status[test_id]["message"] = (f"任务完成，共发送 {sent_count} 条数据")
            logger.info(f"任务 {test_id} 完成，共发送 {sent_count} 条数据")
            task_logger.info(f"任务完成 - 共发送 {sent_count} 条数据")

    except asyncio.CancelledError:
        # 任务被取消
        if test_id in task_manager.task_status:
            task_manager.task_status[test_id]["is_running"] = False
            task_manager.task_status[test_id]["message"] = (f"任务已取消，已发送 {sent_count} 条数据")
        logger.info(f"任务 {test_id} 被取消，已发送 {sent_count} 条数据")
        task_logger.info(f"任务被取消 - 已发送 {sent_count} 条数据")
        raise
    except Exception as e:
        # 任务异常
        if test_id in task_manager.task_status:
            task_manager.task_status[test_id]["is_running"] = False
            task_manager.task_status[test_id]["message"] = f"任务异常: {str(e)}"
        logger.error(f"任务 {test_id} 发生异常: {str(e)}")
        task_logger.error(f"任务异常: {str(e)}")


# API路由
@app.post("/api/v1/dataApi/import", response_model=ApiResponse)
async def import_data(request: DataImportRequest):
    """数据导入接口 - 创建或停止数据发送任务"""

    test_id = request.test_id
    logger.info(f"接收到请求: state={request.state}, test_id={test_id}")

    if request.state.lower() == "start":
        # 启动任务
        if task_manager.is_running(test_id):
            logger.warning(f"任务 {test_id} 已在运行中")
            raise HTTPException(status_code=400, detail=f"任务 {test_id} 已在运行中")

        # 参数验证
        if request.interval <= 0 or request.duration <= 0:
            logger.error(f"参数错误: interval={request.interval}, duration={request.duration}")
            raise HTTPException(status_code=400, detail="间隔时间和持续时间必须大于0")

        if request.group_size <= 0 or request.group_size > 25:
            logger.error(f"参数错误: group_size={request.group_size}")
            raise HTTPException(status_code=400, detail="子组大小必须在1-25之间")

        # 获取有效的Authorization（包含Token获取和连接测试）
        logger.info(f"获取有效Authorization，test_id={test_id}")
        auth_result = await api_client.get_valid_authorization()
        if not auth_result["success"]:
            logger.error(f"获取有效Authorization失败: {auth_result.get('error', '未知错误')}")
            raise HTTPException(
                status_code=400,
                detail=f"获取有效Authorization失败: {auth_result.get('error', '未知错误')}",
            )

        authorization = auth_result["authorization"]
        logger.info(f"Authorization验证成功，test_id={test_id}")

        # 创建任务参数
        task_params = {
            "group_size": request.group_size,
            "interval": request.interval,
            "duration": request.duration,
            "email": api_client.email,
            "authorization": authorization,
        }

        # 启动异步任务
        task_coro = data_sending_task(test_id, request.group_size, request.interval, request.duration, authorization)
        task_manager.start_task(test_id, task_coro, task_params)

        logger.info(f"任务 {test_id} 已启动")

        return ApiResponse(
            success=True,
            message=f"任务 {test_id} 已启动",
            data=task_manager.get_status(test_id),
        )

    elif request.state.lower() == "stop":
        # 停止任务
        if not task_manager.is_running(test_id):
            logger.warning(f"尝试停止未运行的任务 {test_id}")
            return ApiResponse(
                success=False,
                message=f"任务 {test_id} 未在运行",
                data=task_manager.get_status(test_id),
            )

        logger.info(f"停止任务 {test_id}")
        await task_manager.stop_task(test_id)

        return ApiResponse(
            success=True,
            message=f"任务 {test_id} 已停止",
            data=task_manager.get_status(test_id),
        )

    else:
        logger.error(f"无效的state参数: {request.state}")
        raise HTTPException(status_code=400, detail="state参数必须是 'start' 或 'stop'")


@app.get("/api/v1/task/status/{test_id}")
async def get_task_status(test_id: int):
    """获取任务状态"""
    status = task_manager.get_status(test_id)
    return ApiResponse(success=True, message="获取状态成功", data=status)


@app.get("/api/v1/tasks")
async def get_all_tasks():
    """获取所有任务状态"""
    all_status = {}
    for test_id in task_manager.task_status:
        all_status[str(test_id)] = task_manager.get_status(test_id)

    return ApiResponse(success=True, message="获取所有任务状态成功", data=all_status)


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)

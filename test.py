import numpy as np
import pandas as pd
import requests
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import Literal, Iterator
from pathlib import Path


class DataGenerator:
    """重新设计日期系统的数据生成和API请求处理类"""

    def __init__(
        self,
        test_id: int,
        start_time: str,
        interval_value: int,
        interval_type: Literal["year", "month", "day", "hour", "minute", "second"],
        total_count: int,
        api_url: str = "http://101.37.161.59:8002/api/v1/dataApi/import",
        cookie: str = "",
        size: int = 8,
        file_name: str = "output",
        file_type: Literal["xlsx", "xls", "csv"] = "xlsx",
    ) -> None:
        """
        初始化数据生成器
        
        Args:
            test_id: 测试ID
            start_time: 起始时间 (格式: "2025-01-01 08:00:00")
            interval_value: 时间间隔数值 (正整数)
            interval_type: 间隔类型 ("year", "month", "day", "hour", "minute", "second")
            total_count: 数据总量 (生成多少行数据)
            api_url: API请求URL
            cookie: API请求Cookie
            size: 每条记录的数据点数量 (1-25)
            file_name: 输出文件名 (不含扩展名)
            file_type: 文件类型 ("xlsx", "xls", "csv")
        """
        # 基础配置
        self.test_id = test_id
        self.start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        self.interval_value = max(1, interval_value)  # 确保为正整数
        self.interval_type = interval_type
        self.total_count = max(1, total_count)  # 确保至少生成1条数据
        self.size = min(max(size, 1), 25)  # 限制在1-25之间
        
        # 文件配置
        self.file_path = Path(f"{file_name}.{file_type}")
        self.file_type = file_type
        
        # API配置
        self.api_url = api_url
        self.headers = self._build_headers(cookie)
        
        # 预计算时间增量对象
        self.time_delta = self._create_time_delta()

    def _build_headers(self, cookie: str) -> dict:
        """构建HTTP请求头"""
        return {
            "accept": "application/json",
            "Content-Type": "application/json",
            "Cookie": cookie,
            "Connection": "keep-alive",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            "Origin": "http://101.37.161.59:8002",
            "Referer": "http://101.37.161.59:8002/BSA-SPC%20v4.html",
        }

    def _create_time_delta(self):
        """根据间隔类型创建时间增量对象"""
        match self.interval_type:
            case "year":
                return relativedelta(years=self.interval_value)
            case "month":
                return relativedelta(months=self.interval_value)
            case "day":
                return timedelta(days=self.interval_value)
            case "hour":
                return timedelta(hours=self.interval_value)
            case "minute":
                return timedelta(minutes=self.interval_value)
            case "second":
                return timedelta(seconds=self.interval_value)
            case _:
                raise ValueError(f"不支持的时间间隔类型: {self.interval_type}")

    def _time_series_generator(self) -> Iterator[str]:
        """
        时间序列生成器
        从起始时间开始，按照指定间隔生成指定数量的时间点
        """
        current_time = self.start_time
        for _ in range(self.total_count):
            yield current_time.strftime("%Y-%m-%d %H:%M:%S")
            current_time += self.time_delta

    def _create_record(self, inspection_time: str) -> dict:
        """创建单条JSON记录"""
        return {
            "inspection_time": inspection_time,
            "measurements": [{
                "id": self.test_id,
                "data": np.random.randint(596, 604, size=self.size).tolist(),
            }],
            "metadata": {
                "f1": "过程-01", "f2": "操作员-02", "f3": "产品型号-03",
                "f4": "产品-04", "f5": "设备-05", "f6": "供应商-02",
            },
        }

    def _record_to_row(self, record: dict) -> dict:
        """将JSON记录转换为DataFrame行数据"""
        measurement = record["measurements"][0]
        data_values = measurement["data"]
        metadata = record["metadata"]
        
        # 构建完整行数据 (test_id + test_time + v1~v25 + f1~f9)
        row = {
            "test_id": measurement["id"],
            "test_time": record["inspection_time"],
        }
        
        # 添加v1~v25列 (固定25列)
        row.update({
            f"v{i}": data_values[i-1] if i-1 < len(data_values) else "" 
            for i in range(1, 26)
        })
        
        # 添加f1~f9列 (固定9列)
        row.update({
            f"f{i}": metadata.get(f"f{i}", "") 
            for i in range(1, 10)
        })
        
        return row

    def _save_dataframe(self, df: pd.DataFrame) -> None:
        """保存DataFrame到文件"""
        match self.file_type:
            case "xlsx":
                df.to_excel(self.file_path, index=False, engine="openpyxl")
            case "xls":
                df.to_excel(self.file_path, index=False, engine="xlwt")
            case "csv":
                df.to_csv(self.file_path, index=False, encoding="utf-8-sig")
            case _:
                raise ValueError(f"不支持的文件类型: {self.file_type}")

    def process_and_save(self) -> tuple[Path, list[dict]]:
        """
        核心处理方法：生成数据并保存到文件
        返回: (文件路径, JSON数据列表)
        """
        # 流式处理：边生成边转换
        records = []
        rows = []
        
        for time_str in self._time_series_generator():
            record = self._create_record(time_str)
            records.append(record)
            rows.append(self._record_to_row(record))
        
        # 创建DataFrame并设置列顺序
        df = pd.DataFrame(rows)
        column_order = (
            ["test_id", "test_time"] + 
            [f"v{i}" for i in range(1, 26)] + 
            [f"f{i}" for i in range(1, 10)]
        )
        df = df[column_order]
        
        # 保存文件
        self._save_dataframe(df)
        
        print(f"数据已保存到: {self.file_path}")
        print(f"文件包含 {len(df)} 行数据，{len(df.columns)} 列")
        
        return self.file_path, records

    def send_api_request(self, json_data: list[dict]) -> dict:
        """发送API请求"""
        try:
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=json_data, 
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            raise

    def run(self) -> dict:
        """一键执行：生成数据 -> 保存文件 -> 发送API请求"""
        print("开始处理数据...")
        print(f"起始时间: {self.start_time}")
        print(f"时间间隔: {self.interval_value} {self.interval_type}")
        print(f"数据总量: {self.total_count} 条")
        print(f"每条数据点: {self.size} 个")
        
        # 处理并保存数据
        file_path, json_data = self.process_and_save()
        
        # 发送API请求
        try:
            api_result = self.send_api_request(json_data)
            print("API请求成功")
            return {
                "success": True,
                "file_path": str(file_path),
                "records_count": len(json_data),
                "api_response": api_result
            }
        except Exception as e:
            return {
                "success": False,
                "file_path": str(file_path),
                "records_count": len(json_data),
                "error": str(e)
            }

    def preview(self, n: int = 3) -> pd.DataFrame:
        """快速预览数据（不保存文件）"""
        preview_count = min(n, self.total_count)
        
        # 生成预览数据
        records = []
        time_gen = self._time_series_generator()
        
        for _ in range(preview_count):
            time_str = next(time_gen)
            records.append(self._create_record(time_str))
        
        # 转换为DataFrame
        rows = [self._record_to_row(record) for record in records]
        df = pd.DataFrame(rows)
        column_order = (
            ["test_id", "test_time"] + 
            [f"v{i}" for i in range(1, 26)] + 
            [f"f{i}" for i in range(1, 10)]
        )
        df = df[column_order]
        
        print(f"数据预览 (前 {preview_count} 行):")
        print(df)
        return df

    @property
    def info(self) -> dict:
        """获取配置信息"""
        # 计算结束时间
        end_time = self.start_time
        for _ in range(self.total_count - 1):
            end_time += self.time_delta
        
        return {
            "test_id": self.test_id,
            "start_time": str(self.start_time),
            "end_time": str(end_time),
            "interval": f"{self.interval_value} {self.interval_type}",
            "total_count": self.total_count,
            "data_size_per_record": self.size,
            "output_file": str(self.file_path)
        }

    def get_time_preview(self, n: int = 5) -> list[str]:
        """预览将要生成的时间序列"""
        preview_count = min(n, self.total_count)
        times = []
        time_gen = self._time_series_generator()
        
        for _ in range(preview_count):
            times.append(next(time_gen))
        
        print(f"时间序列预览 (前 {preview_count} 条):")
        for i, time_str in enumerate(times, 1):
            print(f"  {i}: {time_str}")
        
        return times


# 使用示例
if __name__ == "__main__":
    # 创建生成器 - 每分钟生成一条数据，共生成120条
    generator = DataGenerator(
        test_id=400,
        start_time="2025-01-01 08:00:00",
        interval_value=1,
        interval_type="minute",
        total_count=120,
        api_url="http://101.37.161.59:8002/api/v1/dataApi/import",
        cookie="afbae3a1cc1fc1de149a7b14c5a4408b=9e6f1e76-9750-41a0-89d0-e19797b2514d._zGV-xNd7n9D0svb-G-dK-SchDo; http_order=id%20desc; http_serverType=nginx; http_rank=list; http_bt_user_info=%7B%22status%22%3Atrue%2C%22msg%22%3A%22%E8%8E%B7%E5%8F%96%E6%88%90%E5%8A%9F!%22%2C%22data%22%3A%7B%22username%22%3A%22188****8888%22%7D%7D; http_pro_end=-1; http_ltd_end=1816131602; http_file_recycle_status=true; http_Path=%2Fwww%2Fwwwroot; XSRF-TOKEN=eyJpdiI6Ik9HRkJLaGF0cWFUdFVxditMQVl3L3c9PSIsInZhbHVlIjoiYVZiY2RwSTlxbjVCMFJ0RG5hNHM1WFlpM0tYTVB6Y0RWbUNpT3cxdlV6amxudlAyOVFtRTB3UlRWV1RadW04UUtCU0xEanVxOEZOMlFTSmQ5VVoxNnNMK2xyWURQUlM3MEdZNjlRQXhNZWhvdHhPVExWNWZUL1ZFMU9FTUliK2siLCJtYWMiOiI1NDFhMjE3MTI3MTA4ZmIxYjM4MTAxMjQ0MzIzNjJjMGQzZjlhOGZhY2NjNTVlYjA3NTA3ZDEwMzZlNTFmY2Y2IiwidGFnIjoiIn0%3D; spc_pro_session=eyJpdiI6IlZDcVkxTHlVU2FyVDY0OXgyWlo5cWc9PSIsInZhbHVlIjoiOTNBaVcxSUhiNUxKbTVlUEgxVkZuaVJJMm5LV0E1UnlwRVBxemRLU3BuZWJmL0hSYWlsTm13citONnNXdmEzeUVCNXNoMnJtRUR0eTBwSC9WTE9IWmF2ZFhJL3BpcVI1SDdBdVNpV0RDYThWWXA3QjdUbHYzZmU3OFQzVitVQ3AiLCJtYWMiOiJkMGEyOGU0YWUwMzgwNTlhYzQ2OTRmYjhhMWY5ZTQ3YTY4NDQ5MjM0MDFkM2RiNWMyOWEwMjVkOGFjOWE4OWJhIiwidGFnIjoiIn0%3D",
        size=7,
        file_name="test_data",
        file_type="xlsx"
    )
    
    # 查看配置信息
    print("配置信息:")
    for key, value in generator.info.items():
        print(f"  {key}: {value}")
    
    # 预览时间序列
    generator.get_time_preview(5)
    
    # 预览数据
    generator.preview(3)
    
    # 一键执行所有操作
    result = generator.run()
    print(f"\n执行结果: {result}")